<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\ReadingSessionResource;
use App\Models\Book;
use App\Models\Language;
use App\Models\ReadingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

final class ReadingSessionsController extends Controller
{
    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
    }

    public function index(Book $book): Collection
    {
        $sessions = ReadingSession::where('book_id', $book->id)
            ->where('user_id', Auth::id())
            ->with(['language'])
            ->orderBy('created_at', 'desc')
            ->get();

        return ReadingSessionResource::collection($sessions);
    }

    public function store(Request $request): array
    {
        $validated = $request->validate([
            'bookId' => 'required|string|exists:books,uuid',
            'languageCode' => 'nullable|string|exists:languages,code',
            'startDate' => 'nullable|date|before_or_equal:today',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'pagesRead' => 'nullable|integer|min:0',
            'totalPages' => 'nullable|integer|min:1',
        ]);

        // Additional validation: pages_read must be <= total_pages if both are provided
        if (isset($validated['pagesRead']) && isset($validated['totalPages'])
            && $validated['pagesRead'] > $validated['totalPages']) {
            throw ValidationException::withMessages([
                'pagesRead' => 'Pages read cannot be greater than total pages.',
            ]);
        }

        $book = Book::where('uuid', $validated['bookId'])->firstOrFail();
        $language = null;

        if (!empty($validated['languageCode'])) {
            $language = Language::where('code', $validated['languageCode'])->first();
        }

        $session = new ReadingSession();
        $session->uuid = Str::uuid()->toString();
        $session->book_id = $book->id;
        $session->user_id = Auth::id();
        $session->language_id = $language?->id;
        $session->start_date = $validated['startDate'] ?? null;
        $session->end_date = $validated['endDate'] ?? null;
        $session->pages_read = $validated['pagesRead'] ?? null;
        $session->total_pages = $validated['totalPages'] ?? null;

        $success = $session->save();

        if ($success) {
            $session->load(['language']);
        }

        return [
            'success' => $success,
            'session' => $success ? new ReadingSessionResource($session) : null,
        ];
    }

    public function update(string $sessionUuid, Request $request): array
    {
        $session = ReadingSession::where('uuid', $sessionUuid)->firstOrFail();

        // Ensure the session belongs to the authenticated user
        if ($session->user_id !== Auth::id()) {
            abort(403, 'Unauthorized');
        }

        $validated = $request->validate([
            'languageCode' => 'nullable|string|exists:languages,code',
            'startDate' => 'nullable|date|before_or_equal:today',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'pagesRead' => 'nullable|integer|min:0',
            'totalPages' => 'nullable|integer|min:1',
        ]);

        // Additional validation: pages_read must be <= total_pages if both are provided
        if (isset($validated['pagesRead']) && isset($validated['totalPages'])
            && $validated['pagesRead'] > $validated['totalPages']) {
            throw ValidationException::withMessages([
                'pagesRead' => 'Pages read cannot be greater than total pages.',
            ]);
        }

        $language = null;
        if (!empty($validated['languageCode'])) {
            $language = Language::where('code', $validated['languageCode'])->first();
        }

        $session->language_id = $language?->id;
        $session->start_date = $validated['startDate'] ?? $session->start_date;
        $session->end_date = $validated['endDate'] ?? $session->end_date;
        $session->pages_read = $validated['pagesRead'] ?? $session->pages_read;
        $session->total_pages = $validated['totalPages'] ?? $session->total_pages;

        $success = $session->save();

        if ($success) {
            $session->load(['language']);
        }

        return [
            'success' => $success,
            'session' => $success ? new ReadingSessionResource($session) : null,
        ];
    }

    public function destroy(string $sessionUuid): array
    {
        $session = ReadingSession::where('uuid', $sessionUuid)->firstOrFail();

        // Ensure the session belongs to the authenticated user
        if ($session->user_id !== Auth::id()) {
            abort(403, 'Unauthorized');
        }

        $success = $session->delete();

        return [
            'success' => $success,
        ];
    }
}
