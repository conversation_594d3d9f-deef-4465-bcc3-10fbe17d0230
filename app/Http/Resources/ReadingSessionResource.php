<?php declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

final class ReadingSessionResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->uuid,
            'bookId' => $this->book->uuid,
            'userId' => $this->user->uuid,
            'language' => $this->language ? new LanguageResource($this->language) : null,
            'startDate' => $this->start_date?->toDateString(),
            'endDate' => $this->end_date?->toDateString(),
            'pagesRead' => $this->pages_read,
            'totalPages' => $this->total_pages,
            'createdAt' => $this->created_at->toISOString(),
            'updatedAt' => $this->updated_at->toISOString(),
        ];
    }
}
