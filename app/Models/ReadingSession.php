<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ReadingSession
 *
 * @property int $id
 * @property string $uuid
 * @property int $book_id
 * @property int $user_id
 * @property int|null $language_id
 * @property Carbon|null $start_date
 * @property Carbon|null $end_date
 * @property int|null $pages_read
 * @property int|null $total_pages
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ReadingSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'book_id',
        'user_id',
        'language_id',
        'start_date',
        'end_date',
        'pages_read',
        'total_pages',
    ];
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'pages_read' => 'integer',
        'total_pages' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }
}
