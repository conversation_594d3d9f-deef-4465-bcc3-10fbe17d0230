import {get} from 'svelte/store';
import ApiCallProgressStore from '$lib/stores/ApiCallProgressStore';
import LocaleStore from '$lib/stores/LocaleStore';

type fetchType = (input: RequestInfo | URL, init?: RequestInit | undefined) => Promise<Response>;

export default abstract class BaseApiClient {
    protected abstract readonly apiUrl: string;
    protected abstract readonly apiKey: string | null;
    private readonly fetch: fetchType;

    constructor(pageLoadFetch?: fetchType) {
        this.fetch = typeof pageLoadFetch === 'undefined' ? fetch : pageLoadFetch;
    }

    protected async get(endpoint: string, data?: Record<string, string>) {
        if (data !== undefined) {
            const prefix = endpoint.includes('?') ? '&' : '?';
            endpoint += prefix + new URLSearchParams(data).toString();
        }

        return await this.call(endpoint);
    }

    protected async post(endpoint: string, data?: object) {
        return await this.call(endpoint, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    protected async put(endpoint: string, data?: object) {
        return await this.call(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    protected async delete(endpoint: string) {
        return await this.call(endpoint, {
            method: 'DELETE',
        });
    }

    protected async upload(endpoint: string, data: FormData) {
        return await this.call(
            endpoint,
            {
                method: 'POST',
                body: data,
            },
            false,
        );
    }

    protected async call(input: RequestInfo | URL, init?: RequestInit, isJsonContentType: boolean = true) {
        ApiCallProgressStore.start();

        const defaultInit: RequestInit = {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-locale': get(LocaleStore),
            },
        };
        if (typeof this.apiKey === 'string') {
            // TypeScript doesn't allow assigning the key conditionally on the initial object
            defaultInit.headers = {
                ...defaultInit.headers,
                Authorization: `Bearer ${this.apiKey}`,
            };
        }
        if (isJsonContentType) {
            defaultInit.headers = {
                ...defaultInit.headers,
                'Content-Type': 'application/json',
            };
        }
        const options: RequestInit = Object.assign(defaultInit, init);

        let response: Response;
        try {
            response = await this.fetch(this.apiUrl + input, options);
        } catch (error) {
            return {
                ok: false,
                status: 500,
                data: {},
            };
        }
        let data;
        try {
            data = await response.json();
        } catch (error) {
            data = {error: response.statusText};
        }

        ApiCallProgressStore.finish();

        return {
            ok: response.ok,
            status: response.status,
            data: data,
        };
    }
}

export interface BaseApiResponse {
    ok: boolean;
    status: number;
    data: object | object[];
}
