<script lang="ts">
    import {createEventDispatcher, onMount} from 'svelte';
    import LanguagesApiClient from '$lib/api/LanguagesApiClient';
    import Button from '$lib/components/ui/Button.svelte';
    import DateInput from '$lib/components/ui/DateInput.svelte';
    import NumberInput from '$lib/components/ui/NumberInput.svelte';
    import Select from '$lib/components/ui/Select.svelte';
    import CalendarSvg from '$lib/components/svg/CalendarSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import type Book from '$lib/domain/Book';
    import type Language from '$lib/domain/Language';
    import type ReadingSession from '$lib/domain/ReadingSession';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';

    export let book: Book;
    export let session: Partial<ReadingSession> = {};
    export let defaultStartDate: string = '';
    export let defaultEndDate: string = '';
    export let isEditing: boolean = false;
    export let isLoading: boolean = false;

    const dispatch = createEventDispatcher();

    let languages: Language[] = [];
    let selectedLanguage: string = '';
    let startDate: string = '';
    let endDate: string = '';
    let pagesRead: string = '';
    let totalPages: string = '';
    let errors: Record<string, string> = {};

    const languagesApiClient = new LanguagesApiClient();

    onMount(async () => {
        await loadLanguages();
        initializeForm();
    });

    async function loadLanguages() {
        try {
            const response = await languagesApiClient.index();
            if (response.ok) {
                languages = response.data;
            }
        } catch (error) {
            console.error('Failed to load languages:', error);
        }
    }

    function initializeForm() {
        // Initialize with session data or defaults
        selectedLanguage = session.language?.value || (book.languages.length > 0 ? book.languages[0].value : '');
        startDate = session.startDate || defaultStartDate;
        endDate = session.endDate || defaultEndDate;
        pagesRead = session.pagesRead?.toString() || '';
        totalPages = session.totalPages?.toString() || book.numberOfPages?.toString() || '';
    }

    function validateForm(): boolean {
        errors = {};

        // Validate dates
        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
            errors.endDate = $t('readingSessions.errors.invalidDates');
        }

        // Validate pages
        const pagesReadNum = parseInt(pagesRead) || 0;
        const totalPagesNum = parseInt(totalPages) || 0;

        if (pagesRead && totalPages && pagesReadNum > totalPagesNum) {
            errors.pagesRead = $t('readingSessions.errors.invalidPages');
        }

        return Object.keys(errors).length === 0;
    }

    function handleSave() {
        if (!validateForm()) {
            return;
        }

        const sessionData = {
            bookId: book.uuid,
            languageCode: selectedLanguage || undefined,
            startDate: startDate || undefined,
            endDate: endDate || undefined,
            pagesRead: pagesRead ? parseInt(pagesRead) : undefined,
            totalPages: totalPages ? parseInt(totalPages) : undefined,
        };

        dispatch('save', sessionData);
    }

    function handleAddSession() {
        dispatch('addSession');
    }

    function handleCancel() {
        dispatch('cancel');
    }

    // Convert languages array to format expected by Select component
    $: languageOptions = languages.map(lang => lang.value);
    $: selectedLanguageText = languages.find(lang => lang.value === selectedLanguage)?.text || selectedLanguage;
</script>

<div class="space-y-4">
    <!-- Language Selection -->
    <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <LanguageSvg svgClass="inline h-4 w-4 mr-1" />
            {$t('readingSessions.language')}
        </label>
        <Select
            title={$t('readingSessions.selectLanguage')}
            options={languageOptions}
            bind:value={selectedLanguage}
        />
    </div>

    <!-- Date Fields -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <CalendarSvg svgClass="inline h-4 w-4 mr-1" />
                {$t('readingSessions.startDate')}
            </label>
            <DateInput
                title={$t('readingSessions.startDate')}
                bind:value={startDate}
                error={errors.startDate}
            />
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <CalendarSvg svgClass="inline h-4 w-4 mr-1" />
                {$t('readingSessions.endDate')}
            </label>
            <DateInput
                title={$t('readingSessions.endDate')}
                bind:value={endDate}
                error={errors.endDate}
            />
        </div>
    </div>

    <!-- Pages Fields -->
    <div class="grid grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <BookOpenSvg svgClass="inline h-4 w-4 mr-1" />
                {$t('readingSessions.pagesRead')}
            </label>
            <NumberInput
                title={$t('readingSessions.pagesRead')}
                bind:value={pagesRead}
                min={0}
            />
            {#if errors.pagesRead}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors.pagesRead}</p>
            {/if}
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <BookOpenSvg svgClass="inline h-4 w-4 mr-1" />
                {$t('readingSessions.totalPages')}
            </label>
            <NumberInput
                title={$t('readingSessions.totalPages')}
                bind:value={totalPages}
                min={1}
            />
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-2 pt-4">
        <Button
            title={$t('readingSessions.save')}
            callback={handleSave}
            extraClass="flex-1"
        />

        {#if !isEditing}
            <Button
                title={$t('readingSessions.addSession')}
                callback={handleAddSession}
                primary={false}
                extraClass="flex-1"
            />
        {/if}

        {#if isEditing}
            <Button
                title={$t('readingSessions.cancel')}
                callback={handleCancel}
                primary={false}
                extraClass="flex-1"
            />
        {/if}
    </div>
</div>
